# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Next.js 15 AI chatbot application called "Prompt Repo" that supports multiple AI models (<PERSON>AI, <PERSON>, Gemini). The app is currently in development with planned features including AI conversations, reasoning chain visualization, native model search support, MCP tool calling, and OAuth authentication.

## Commands

### Development
- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production  
- `npm start` - Start production server
- `npm run lint` - Run ESLint

### Database
- `npm run initdb` - Initialize/push database schema using Drizzle Kit
- Database uses PostgreSQL with Drizzle ORM
- Schema files: `lib/db/schema.ts` (main), `lib/db/auth-schema.ts` (auth)
- Migrations: `lib/db/migrations/`

## Architecture

### Tech Stack
- **Framework**: Next.js 15 with App Router
- **Database**: PostgreSQL with Drizzle ORM  
- **AI**: AI SDK v5 (`ai` package, `@ai-sdk/react`)
- **Auth**: Better Auth with GitHub/Google OAuth support
- **State**: Zustand for client state, @tanstack/react-query for data fetching
- **UI**: Shadcn/ui components, Tailwind CSS
- **Validation**: Zod

### Key Structure
- `/app/` - Next.js App Router pages
  - `/api/auth/[...all]/` - Better Auth API endpoints
  - `/api/chat/` - AI chat API endpoint
  - `/chat/` - Chat UI pages
- `/components/` - React components
  - `/ai-elements/` - AI-specific components (conversation, message, code-block, etc.)
  - `/ui/` - Shadcn/ui components
- `/lib/` - Utilities and configuration
  - `/db/` - Database schema and connection
  - `auth.ts` - Better Auth configuration
  - `auth-client.ts` - Client-side auth utilities

### Database Schema
- `Chat` table: Stores chat sessions with user association, visibility settings
- `Message` table: Stores chat messages with parts (content), attachments, annotations
- Auth tables managed by Better Auth adapter

### Authentication
- Better Auth with Drizzle adapter
- Supports email/password and OAuth (GitHub, Google)
- No email verification required, auto sign-in enabled
- Environment variables needed: `GITHUB_CLIENT_ID/SECRET`, `GOOGLE_CLIENT_ID/SECRET`, `DATABASE_URL`

### UI Components
- Custom AI elements for chat interface (`ai-elements/`)
- React Markdown with KaTeX math support
- Syntax highlighting for code blocks
- Collapsible reasoning display
- Web preview capabilities