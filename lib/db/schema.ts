import type { InferSelectModel } from 'drizzle-orm';
import {
  pgTable,
  varchar,
  timestamp,
  json,
  uuid,
  text,
  boolean,
} from 'drizzle-orm/pg-core';
import { user } from './auth-schema';

export const chat = pgTable('Chat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  createdAt: timestamp('createdAt').notNull(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
  title: text('title').notNull(),
  userId: text('userId')
    .notNull()
    .references(() => user.id),
  visibility: varchar('visibility', { enum: ['public', 'private'] })
    .notNull()
    .default('private'),
  isPinned: boolean('isPinned').notNull().default(false),
});

export const message = pgTable('Message', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id, {
      onDelete: 'cascade',
    }),
  parentMessageId: uuid('parentMessageId'),
  role: varchar('role').notNull(),
  parts: json('parts').notNull(),
  attachments: json('attachments').notNull(),
  createdAt: timestamp('createdAt').notNull(),
  annotations: json('annotations'),
  isPartial: boolean('isPartial').notNull().default(false),
  selectedModel: varchar('selectedModel', { length: 256 }).default(''),
  selectedTool: varchar('selectedTool', { length: 256 }).default(''),
});

export type DBMessage = InferSelectModel<typeof message>;
export type Chat = InferSelectModel<typeof chat>;
export * from './auth-schema';