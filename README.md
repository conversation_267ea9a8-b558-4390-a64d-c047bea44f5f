## 简介
当前项目是一个 AI 聊天机器人，还在开发阶段，很多功能尚未完成，计划的功能有：

* AI 对话，支持 OpenAI、<PERSON>、Gemini 等多种模型
* 推理模型思维链的展示
* 原生的模型自带搜索支持
* MCP 工具调用
* 使用 Google 、Github 、邮箱的账号体系

## 技术栈

1. Next.js 15 
2. Drizzle ORM: 结合 PostgreSQL 使用
3. AI SDK v5: 用于 AI 对话，注意大版本号为 5，使用 context7 时可搜索 v5_ai-sdk_dev
4. @tanstack/react-query: 用于数据获取
5. Better Auth: 用于用户认证，使用 context7 时可搜索 better-auth
6. Zustand: 用于状态管理
7. Shadcn/ui: 用于 UI 组件
8. Zod: 用于数据验证
9. React Markdown: 用于渲染 Markdown
10. Sonner: 用于通知
11. Lucide React: 用于图标